<template>
  <div class="centerRight2">
    <div>
      <n-data-table
        :columns="columns"
        :data="data"
        :bordered="false"
        :bottom-bordered="false"
        :virtual-scroll="true"
        size="small"
        :max-height="216" />
    </div>
  </div>
</template>

<script lang="ts">

import { defineComponent, reactive, ref, h } from 'vue'
import { NDataTable } from 'naive-ui'
// @ts-ignore
import numOne from '@/assets/numOne.png'
// @ts-ignore
import numx from '@/assets/numx.png'
// @ts-ignore
import numTwo from '@/assets/numTwo.png'
// @ts-ignore
import numThree from '@/assets/numThree.png'

function createColumns() {
  return [
    {
      title: '序号',
      key: 'key',
      align: 'center',
      width: 100,
      render(row) {
        if (row.key === 1) {
          return h('img', {
            src: numOne,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else if (row.key === 2) {
          return h('img', {
            src: numTwo,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else if(row.key === 3) {
          return h('img', {
            src: numThree,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else {
          // 如果不是 1，给 div 添加背景图并渲染 key
          return h(
            'div',
            {
              style: {
                backgroundImage: `url(${numx})`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center center',
                width: '80px',
                height: '26px',

                color: '#fff',
              },
            },
            row.key.toString(),
          )
        }
      },
    },
    {
      title: '仓库',
      key: 'name',
    },
    {
      title: '物资名称',
      key: 'age',
      width: 400,
      ellipsis: true,
    },
    {
      title: '库存/预警值',
      key: 'address',
    },
  ]
}

function createData() {
  return [
    {
      key: 1,
      name: '物资仓库A',
      age: '大开杀戒代付款盛世嫡妃金卡拉萨的反啊实打实大伤口大数据库击看亮机卡',

      address: '22/33',
    },
    {
      key: 2,
      name: '到付件可视角度',
      age: '尽快来得及防守打法金坷垃就开了防守打法监考老师',
      address: '123/456',
    },
    {
      key: 3,
      name: 'Joe Black',
      age: 32,
      address: '66/2123',
    },
    {
      key: 4,
      name: '仓库e',
      age: 32,
      address: '4444/6565',
    },
    {
      key: 5,
      name: '仓库c',
      age: '大开杀戒代付款盛世嫡妃金卡拉萨的反击看亮机卡',
      address: '664/6743',
    },
    {
      key: 6,
      name: '仓库d',
      age: 32,
      address: '234/654',
    },
    {
      key: 7,
      name: 'Joe Black',
      age: 32,
      address: '11345/2323',
    },
  ]
}

export default defineComponent({
  components: {
    NDataTable,
  },
  setup() {
    return {
      data: createData(),
      columns: createColumns(),
    }
  },
})
</script>

<style lang="scss" scoped>
$box-height: 257px;
$box-width: 812px;
.centerRight2 {
  height: $box-height;
  width: $box-width;
  background-color: pink;
}
</style>
